import { ChangeDetectorRef, Component, EventEmitter, Output } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { ScheduleDetailsCalendarService } from './services/schedule-details-calendar.service';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { ActivatedRoute } from '@angular/router';
import { IData, IParamGroup, IReportParamData } from 'src/app/shared-services/ep-api-handler/models/report-param-data-model';
import { ILocationGroup } from '../models/location-group.model';
import { MatCalendar, MatDatepickerModule } from '@angular/material/datepicker';
import { ILocation } from '../models/location.model';
import { AppointmentProviders } from 'src/app/modules/dashboard/models/Appointment-Info-model';

@Component({
  selector: 'epividian-schedule-details-calendar',
  templateUrl: './schedule-details-calendar.component.html',
  styleUrl: './schedule-details-calendar.component.scss'
})
export class ScheduleDetailsCalendarComponent {
  public selectedDropModifier: string = "Location";
  public selectedDropValue: any; //{ key: number; value: string } | null = null;
  public selectedDate: Date = new Date();
  public currentDate: Date = new Date();
  public yesterdayDate: Date = new Date(new Date().setDate(new Date().getDate() - 1));
  public tomorrowDate: Date = new Date(new Date().setDate(new Date().getDate() + 1));
  public sections: any;
  public locations: IReportParamData = {} as IReportParamData;
  public providers: AppointmentProviders[] = [];
  public selectedLocation: ILocation = {} as ILocation;
  public selectedProvider: string = "";
  public siteId: string = "";

    // Create an EventEmitter to emit selectedDate
    @Output() dateSelected: EventEmitter<Date> = new EventEmitter<Date>();
    @Output() providerSelected: EventEmitter<string> = new EventEmitter<string>();
    @Output() locationSelected: EventEmitter<ILocation> = new EventEmitter<ILocation>();
    
  constructor(
    public calendarService: ScheduleDetailsCalendarService,
    private userContext: UserContext,
  ) {}

  ngOnInit(){
    this.siteId = this.userContext.GetCurrentSiteValue().toString();
    this.getLocations(this.siteId);
    this.getproviders(this.siteId, new Date());
  }

  getLocations(siteId: string){
    this.calendarService.getLocations(this.siteId).subscribe(res => {
      this.locations = res;
      this.getLocationGroups(this.locations);
    });
  }

  getLocationGroups(locations: IReportParamData){
    let locationgroups: any[] = [];
    locations.groups.forEach((group, i) => {
      let temp = {
        label: group.name,
        options: locations.data.filter((f) => f.groupId === i)
      };
      locationgroups.push(temp);
    });
    this.sections = locationgroups

    // Set default location
    if (!this.selectedLocation.id && locations.data.length > 0){
        let location = {
          id: locations.data[0].key.toString(),
          name: locations.data[0].value
        }
        this.selectedLocation = location;
        this.selectedDropValue = locations.data[0];
        this.emitSelectedDropdownValue();
    }
  }

  getproviders(siteId: string, date: Date){
    this.calendarService.getProviders(siteId,date).subscribe(res => {
      this.providers = res;
      this.getProviderGroups(this.providers)
    });
  }

  getProviderGroups(providers: AppointmentProviders[]){
    const groups = [...new Set(providers.map(item => item.group))];
    let provider: any[] = [];
    let providergroups: any[] = [];

    groups.forEach((group, i) => {
      let filteredData = providers.filter((f) => f.group === group);
      provider = filteredData.map(item => ({
          groupId: i,
          key: item.provideR_ID,
          value: item.fulL_NM
      }));
      let temp = {
        label: group,
        options: provider
      }
      providergroups.push(temp);
    });
    if (this.selectedDropModifier == "Provider"){
      if (!this.selectedProvider && provider.length > 0) {
        this.selectedProvider = providergroups[0].options[0].key;
        this.selectedDropValue = providergroups[0].options[0];
        this.sections = providergroups;
      }
    }
  }

  updateDropSections(modifier: string){
    //this.selectedDropValue = null;
    if(modifier == "Provider"){
      this.selectedDropModifier = "Provider";
      this.getProviderGroups(this.providers)
      this.selectedLocation = {} as ILocation;
      this.emitSelectedDropdownValue();
    }
    else{
      this.selectedDropModifier = "Location";
      this.getLocationGroups(this.locations);
      this.selectedProvider = "";
      this.emitSelectedDropdownValue();
      
    }
  }

  optionSelected(selectedValue: { key: number; value: string } | null){
    if (selectedValue != null){
      if(this.selectedDropModifier == "Provider"){
        this.selectedProvider = selectedValue.key.toString();
        this.emitSelectedDropdownValue();
      }
      else{
        let location = {
          id: selectedValue.key.toString(),
          name: selectedValue.value
        }
        this.selectedLocation = location;
        this.emitSelectedDropdownValue();
      }
    }
  }

  setDate(day: string, calendar: MatCalendar<Date>){
    switch (day) {
      case 'Yesterday':
        this.selectedDate = this.yesterdayDate
        break;
      
      case 'Today':
        this.selectedDate = this.currentDate;
        break;
      
      case 'Tomorrow':
        this.selectedDate = this.tomorrowDate
        break;
    }
    calendar.activeDate = this.selectedDate;
    this.emitSelectedDate();
  }

  isSameDate(date1: Date, date2: Date): boolean {
  return date1.getDate() === date2.getDate() &&
         date1.getMonth() === date2.getMonth() &&
         date1.getFullYear() === date2.getFullYear();
  }

  // Function to emit the selected date
  emitSelectedDate() {
    this.dateSelected.emit(this.selectedDate);
  }
  emitSelectedDropdownValue() {
    this.providerSelected.emit(this.selectedProvider);
    this.locationSelected.emit(this.selectedLocation);
  }
}
