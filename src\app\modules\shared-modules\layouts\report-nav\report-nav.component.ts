import { Component, OnInit, Injectable, ElementRef, ViewChildren, QueryList } from '@angular/core';
import { Router } from '@angular/router';
import { IMenuSections } from 'src/app/modules/shared-modules/layouts/models/menu-item.model';
import { LayoutService } from 'src/app/modules/shared-modules/layouts/services/layout/layout.service'
import { IReportList } from 'src/app/shared-services/ep-api-handler/models';
import { MatExpansionPanel } from '@angular/material/expansion';

@Injectable({
  providedIn: 'root'
})
@Component({
  selector: 'epividian-reportNav',
  templateUrl:'./report-nav.component.html',
  styleUrls: ['./report-nav.component.scss']
})

export class ReportNavComponent implements OnInit {
  @ViewChildren('expansionPanel') expansionPanels!: QueryList<MatExpansionPanel>;

  public menuSections: IMenuSections[] = [];
  public showReportNav: boolean = false;
  public currentReportSection: string = "";
  public currentReport: string = "";
  panelOpenState: boolean = false;
  allExpandState = false;
  private reportPath: string = "";

  constructor(public layoutService: LayoutService, private router: Router, public elem: ElementRef) {}

  ngOnInit() {
  }

  ngAfterViewInit(){
    this.layoutService.loadNavMenu();
    this.showReportNav = this.layoutService.getShowMenu();
    this.layoutService.menuSections.subscribe ( (sections) => {
      this.menuSections = sections;
      this.GetCurrentReport();
    });
  }

  NavSelect(path: IReportList)
  {

    //This is needed to force the boldreport component to reload completly otherwise the report does not get re-initialized.
    let reportviewerObj = this.elem.nativeElement.querySelector("bold-reportviewer");
    if (reportviewerObj!==null)
    {
      reportviewerObj.destroy;
    }

    if (path.reportFileName.includes("CustomQuery/Criteria"))
    {
      this.reportPath = `/Dashboard/Report/${path.siteId}/${path.reportFileName.replace(".rdl","")}`
    }
    else {
      if (path.reportFileName.toLowerCase().includes(".rdl"))
      {
        this.reportPath = `/Dashboard/ReportRedirect/${path.siteId}/${path.reportFileName.replace(".rdl","")}`
      }
      else
      {
        this.reportPath = `/Dashboard/${path.reportFileName}/${path.siteId}`
      }

      //this.reportPath = `/Report/${path.siteId}/${path.reportFileName.replace(".rdl","")}`
    }

    this.redirectTo(this.reportPath,path);
  }

  toggleSideExpand(tmpValue: boolean) {
    this.allExpandState = tmpValue;
  }

  GetCurrentReport(){
    this.menuSections.forEach( section => {
      section.reportsForSection.forEach( report => {
        if(this.router.url.split('/')[4] == 'CustomQuery'){
          this.currentReportSection = "Custom Queries and Reports";
          this.currentReport = "Custom Query";
        }
        if(this.router.url.split('/')[2] === 'QualityMeasures'){
          this.currentReportSection = "HIV Reports";
          this.currentReport = "Quality Measures - Angular";
        }
        if(this.router.url.split('/')[4] == report.reportFileName.replace(".rdl","")){
          this.currentReportSection = report.categoryNm;
          this.currentReport =  report.reportName;
        }
      })
    });
  }

  redirectTo(uri:string, path: IReportList){
    this.router.navigateByUrl(uri, {
      replaceUrl: true,
      onSameUrlNavigation: 'reload',
      state: {
        reportInfo: JSON.stringify(path)
      }
    });
  }
}
