{"name": "chorus-portal", "version": "2.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "node updateVersion.js && ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "update-boldreport-packages": "node updateBoldReportPackages.js"}, "private": true, "dependencies": {"@angular/animations": "~17.3.8", "@angular/common": "~17.3.8", "@angular/compiler": "~17.3.8", "@angular/core": "~17.3.8", "@angular/forms": "~17.3.8", "@angular/material": "^16.2.8", "@angular/platform-browser": "~17.3.8", "@angular/platform-browser-dynamic": "~17.3.8", "@angular/router": "~17.3.8", "@angular/service-worker": "~17.3.8", "@boldreports/angular-reporting-components": "^6.3.27", "@boldreports/global": "^6.3.27", "@ng-select/ng-select": "^12.0.7", "bootstrap": "^5.2.3", "chart.js": "^4.4.9", "file-icon-vectors": "^1.0.0", "file-saver": "^2.0.5", "jspdf": "^2.5.1", "jspdf-autotable": "^3.5.28", "lodash": "^4.17.21", "moment": "^2.29.4", "ngx-device-detector": "^7.0.0", "ngx-spinner": "^16.0.0", "ngx-toastr": "~17.0.2", "rxjs": "^7.4.0", "tabulator-tables": "^6.3.1", "tslib": "^2.5.0", "uuid": "^9.0.1", "xlsx": "^0.18.5", "zone.js": "^0.14.5"}, "devDependencies": {"@angular-devkit/build-angular": "~17.3.7", "@angular/cli": "~17.3.7", "@angular/compiler-cli": "~17.3.8", "@boldreports/types": "^6.3.27", "@types/jasmine": "^4.0.0", "@types/node": "^18.0.0", "gzipper": "^7.2.0", "jasmine-core": "~5.0.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.1.0", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "typescript": "~5.4.5", "webpack-bundle-analyzer": "^4.9.1"}}