import { Component, EventEmitter, Input, input, Output, SimpleChanges, ViewChild  } from '@angular/core';
import {MatTooltipModule} from '@angular/material/tooltip';
import { HuddleDetail } from '../models/huddle-detail.model';

interface FilterOption {
  label: string;
  flagBit
}

@Component({
  selector: 'epividian-huddle-filter',
  templateUrl: './huddle-filter.component.html',
  styleUrl: './huddle-filter.component.scss'
})
export class HuddleFilterComponent {

  @Output() filterSelected: EventEmitter<string> = new EventEmitter<string>();
  @Input() huddleAppointments: HuddleDetail[] = [];

  public test: number = 0;

  //@ViewChild('tooltip') tooltip: MatTooltipModule;

  public selectedFilter: string = "All Appointments";
  public selectedIndex: number = 0;

  filters: FilterOption[] = [
    { label: 'All Appointments', flagBit: 3},
    { label: 'Targeted quality gaps', flagBit: 0},
    { label: 'Annuals needed', flagBit: 8},
    { label: 'New Patients', flagBit: 7},
    { label: 'Vaccines & injections', flagBit: 4},
    { label: 'Heavy comorbidities', flagBit: 6},
    { label: 'Recent hospitalizations', flagBit: 2},
    { label: 'No-show risk', flagBit: 5}
  ];

  tooltipMessage = 'Additional information goes here.';
  isTooltipDisabled = true;

  constructor() {
    this.filters.sort( (prev,curr) => prev.label.localeCompare(curr.label));
  }

  ngOnInit(){
    this.emitSelectedFilter();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['huddleAppointments']) {
      this.selectFilter(0);
    }
  }

  // Get number of unique patients with appointments that match that filter
  getAppointmentsWithFlagSet(flagPosition: number): number {
    const numberOfUniquePatients = new Set(
      this.huddleAppointments
        .filter(appointment => this.isFlagSet(appointment, flagPosition))
        .map(appointment => appointment.demographicsId)
      );
    return numberOfUniquePatients.size;
  }

  isFlagSet(huddleDetail: HuddleDetail, flagPosition: number): boolean {
    // Ensure that the binary string exists and is long enough to check the flag position
    if (huddleDetail.huddleFlags && huddleDetail.huddleFlags.length > flagPosition) {
      return huddleDetail.huddleFlags.charAt(huddleDetail.huddleFlags.length - 1 - flagPosition) === '1';
    }
    return false;
  }

  selectFilter(selectedIndex: number) {
    this.selectedIndex = selectedIndex;
    this.selectedFilter = this.filters[selectedIndex].label;
    this.emitSelectedFilter();
  }

  selectItem(filter: string) {
    this.selectedFilter = filter;
  }

  emitSelectedFilter() {
    this.filterSelected.emit(this.selectedFilter);
  }
}
  

