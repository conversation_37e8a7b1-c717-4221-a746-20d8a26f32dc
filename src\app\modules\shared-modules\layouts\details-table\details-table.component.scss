.wrapper{
    width: 100%;
    height: 90vh;
    border-radius: 15px;
    padding: 10px;
    margin-left: 10px;
    background: #EAEAEA ;
    border: 0.8px solid lightgrey;
    font-family: "Museo 500", Arial, sans-serif;
}

.aptTitle{
    font-size: 18px;
    font-family: Museo500-Regular;
    color: #0071BC;
    text-align: center;
}

.aptSubtitle {
    text-align: center;
    margin-bottom: 10px;
    color: #666666;
    font-family: "inter";
}

.detailsWrapper{
    display: flex;
    flex-direction: column;
    background: white;
    padding: 10px 10px 10px 0;
    border-radius: 15px;
}

.appointmentsWapper {
    max-height: 76vh;
    overflow-y: scroll;
}

.appointmentHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid lightgrey;
    color: #999999;
    font-size: 12px;
}

.appointment {
    display: flex;
    min-height: 110px;
    padding: 5px 10px 5px 0;
    border-bottom: 1px solid lightgrey;

    &:last-child{
        border-bottom: none;
    }
}

.appointmentDetails{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    text-align: center;
    padding-right: 15px;
    font-family: "MuseoSans-500";
    font-size: 14px;
    color: #999999;
}

.time-col {
    flex: 2;
    display: flex;
    align-items: center;
}

.time{
    padding-bottom: 10px;
    font-family: "MuseoSans-700";
    font-size: 18px;
    color: #7b7b7b;
}

.timeData{
    justify-content: center;
}

.statusDesc{
    color: red;
    font-family: "MuseoSans-300";
    font-size: 12px;
}

.statusBar{
    width: 5px;
    margin-right: 10px;
  }

  .scheduled{
    background: lightblue;
  }
  .noshow{
    background: yellow;
  }
  .completed{
    background: #0071BC;
  }
  .canceled{
    background: red;
  }
  .canceledbyprovider{
    background: red;
  }
  .canceledbyadmin{
    background: red;
  }
  .canceledbypatient{
    background: red;
  }

.patient-col {
    flex: 2;
}

.patientWrapper{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: baseline;
    height: 100%;
}

.patientName{
    cursor: pointer;
    padding-bottom: 20px;
    font-family: "MuseoSans-700";
    font-size: 16px;
    color: #333333;
}

.tag {
    background-color: #e0e0e0;
    padding: 2px 5px;
    border-radius: 4px;
    margin-right: 4px;
    font-size: smaller;
}

.quality-gaps-col{
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.qualityGapBadge {
    background-image: url('../../../../../assets/images/Intervention_icon2.svg');
    font-family: 'Roboto', sans-serif;
    font-weight: 700;
    font-size: 14px;
    width: 45px;
    height: 45px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    padding-bottom: 3px;
    padding-right: 1px;
    color: #0071BC;
    text-align: center;
    cursor: pointer;
}

.details-col {
    flex: 3;
    display: flex;
    .details-list {
        list-style: none;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding: 0;
        margin: 0;
        font-family: "MuseoSans-300";
        font-size: 15px;
        color: #7b7b7b;
    }
        li {
            margin-bottom: 4px;
            &:before{
                margin-right: 4px;
            }
        }
}

.reportLink {
    flex: 2;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: x-large;
    cursor: pointer;
}

.reportLinkButton {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    color: #66a9d7;
    border: #66a9d7;
    border-style: solid;
    border-width: 1px;
    border-radius: 10px;
    padding: 2px 5px;
}

.reportLinkButton:hover {
    color: #0071BC;
    border-color: #0071BC;
}

.flowsheetArrow{
    background-image: url('../../../../../assets/images/chevron_right_24dp_FILL0_wght400_GRAD0_opsz24.svg');
    width: 30px;
    height: 30px;
    background-repeat: no-repeat;
    background-position: center;
    margin-right: -11px;
}

.expandCollapse {
    display: flex;
    flex: 2;
    text-align: right;
    justify-content: center;
    margin-top: 10px;
    margin-right: 10px;
    font-family: "MuseoSans-500";
    font-size: 11px;
}

.showAll {
    color: #0071BC;
    text-decoration: none;
    cursor: pointer;
  }
  
  .showAll:hover {
    color: #66a9d7;
  }

  .collapseButton{
    padding: 2px;
  }
