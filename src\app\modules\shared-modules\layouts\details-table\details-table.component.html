<div class="wrapper">
    <div class="aptTitle">{{numberOfAppointments}} Appointments for {{numberOfPatients }} Patients</div>
    <div class="aptSubtitle">{{selectedDate | date: 'MMMM d, y'}} {{ selectedLocation.name}}</div>
    <!-- Dev divs to show data being passed back and forth between components -->
    <!-- 
        <div class="subtitle">Selected Date: {{ selectedDate }}</div>
        <div class="subtitle">Selected location: {{ selectedDate }}</div>
        <div class="subtitle">Selected provider: {{ selectedProvider }}</div>
        <div class="subtitle">Selected filter: {{ selectedFilter }}</div>
        <div class="subtitle">selcted filter number {{getAppointmentsWithFlagSet(3)}}</div>
    -->
        
    <div class="detailsWrapper">
        <div class="appointmentHeader">
            <div class="statusBar"></div>
            <div class="time-col">Appointment</div>
            <div class="patient-col">Patient</div>
            <div class="quality-gaps-col">Quality Gaps</div>
            <div class="details-col">Details</div>
            <div class="expandCollapse">
                <div class="showAll collapseButton" (click)="setShowHideAll(true)"> + Expand All </div> 
                <div class="collapseButton"> / </div> 
                <div class="showAll collapseButton" (click)="setShowHideAll(false)"> - Collapse All </div>
            </div>
        </div>
        <div class="appointmentsWapper">
            <div class="appointment" *ngFor="let appointment of detailData">
                <div class="statusBar" ngClass="{{getStatus(appointment.statusDescription ?? '')}}"></div>
                <div class="time-col timeData">
                    <div class="appointmentDetails">
                        <div class="time">{{ appointment.scheduleTime | date: 'h:mm a' }}</div>
                        <div class="appointmentType" [title]="appointment.appointmentType">
                            {{ appointment.appointmentType.length > 22 
                            ? (appointment.appointmentType | slice:0:22) + '...' 
                            : appointment.appointmentType }}
                        </div>
                        <div class="providerName">with {{ appointment.providerName.replace("_", " ") }}</div>
                        <div class="statusDesc" *ngIf="appointment.statusDescription == 'Canceled'">{{appointment.statusDescription}}</div>
                    </div>
                </div>
                <div class="patient-col">
                    <div class="patientWrapper">
                        <div class="patientName" (click)="showFlowSheetHIVReport(appointment)">{{ appointment.patientFullName }} ({{appointment.demographicsId}})</div>
                        <div class="tags">
                            <img src={{appointment.FlowSheetImage}} (click)="showFlowSheetHIVReport(appointment)" style="cursor: pointer">
                        </div>
                    </div>
                </div>
                <div class="quality-gaps-col">
                    <div class="qualityGapBadge" (click)="showRecordsQualityGapReport(appointment)">
                        <span>{{appointment.measureGap}}</span>
                    </div>
                </div>
                <div class="details-col">
                    <ul class="details-list">
                        <li *ngFor="let detail of getDetailsList(appointment)">{{ detail }}</li>
                        <li class="showAll" *ngIf="!appointment.showAllDetails && appointment.details.length > 3" (click)="setShowAlldetails(appointment, true)">+{{appointment.details.length - 3}} more</li>
                        <li class="showAll" *ngIf="appointment.showAllDetails && appointment.details.length > 3" (click)="setShowAlldetails(appointment, false)">- Show less</li>
                    </ul>
                </div>
                <div class="reportLink" (click)="showFlowSheetHIVReport(appointment)"> 
                    <div class="reportLinkButton">
                        <div>Flowsheet</div> <div class="flowsheetArrow"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
